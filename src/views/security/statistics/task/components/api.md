---
title: 安晨
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 安晨

Base URLs:

# Authentication

# 国铁PC/任务监控

## GET 获取模型调用情况

GET /admin/business/siteTaskStatisticsAnalysis/getModelCallCount

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|
|siteName|query|string| 否 |none|
|timeType|query|string| 否 |none|
|eventType|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "操作成功",
  "token": null,
  "uri": null,
  "data": {
    "series": [
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "绝缘靴_手套识别",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "验电检测",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "安全帽_工装",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "挂接地线杆检测",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "上道工器具识别",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "接地靴识别",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "下道工器具识别",
        "type": "line"
      },
      {
        "data": [
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0,
          0
        ],
        "name": "人脸识别",
        "type": "line"
      }
    ],
    "days": [
      "2025-08-01",
      "2025-08-02",
      "2025-08-03",
      "2025-08-04",
      "2025-08-05",
      "2025-08-06",
      "2025-08-07",
      "2025-08-08",
      "2025-08-09",
      "2025-08-10",
      "2025-08-11",
      "2025-08-12",
      "2025-08-13",
      "2025-08-14"
    ]
  },
  "request_time": null,
  "response_time": null,
  "cost_time": null,
  "debug_image_url": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» token|null|true|none||none|
|» uri|null|true|none||none|
|» data|object|true|none||none|
|»» series|[object]|true|none||none|
|»»» data|[integer]|true|none||none|
|»»» name|string|true|none||none|
|»»» type|string|true|none||none|
|»» days|[string]|true|none||none|
|» request_time|null|true|none||none|
|» response_time|null|true|none||none|
|» cost_time|null|true|none||none|
|» debug_image_url|null|true|none||none|

# 数据模型

